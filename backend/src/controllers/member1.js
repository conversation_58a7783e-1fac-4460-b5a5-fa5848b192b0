const { executeQuery, executeQuerySingle, executeNonQuery } = require('../database/db')

// 獲取會員資訊
const getMemberInfo = async (req, res) => {
  try {
    // 從認證中間件獲取用戶 ID
    const userId = req.user.id

    // 查詢會員資訊
    const memberInfo = await executeQuerySingle(
      `
      SELECT id, userId, name, mail, phone, address, birthday, gender, bonusPoints, createdAt as registerDate
      FROM users
      WHERE id = ? AND isDelete = 0
    `,
      [userId],
    )

    if (!memberInfo) {
      return res.status(404).json({ success: false, message: '找不到會員資訊' })
    }

    // 計算會員等級
    let level = '一般會員'
    if (memberInfo.bonusPoints >= 1000) {
      level = '黃金會員'
    }
    if (memberInfo.bonusPoints >= 5000) {
      level = '白金會員'
    }
    if (memberInfo.bonusPoints >= 10000) {
      level = '鑽石會員'
    }

    // 添加會員等級
    memberInfo.level = level

    res.status(200).json({
      success: true,
      resData: memberInfo,
    })
  } catch (error) {
    console.error('獲取會員資訊時發生錯誤:', error)
    res.status(500).json({ success: false, message: '獲取會員資訊失敗，請稍後再試' })
  }
}

// 獲取會員訂單
const getMemberOrders = async (req, res) => {
  try {
    // 從認證中間件獲取用戶 ID
    const userId = req.user.id

    // 查詢會員訂單，包含訂購人、收貨人和付款狀態欄位
    const orders = await executeQuery(
      `
      SELECT id, orderId, total, status, products, orderer, orderer_phone,
             receiver, receiver_phone, phone, address, detailed_address,
             cooperative, invoice_title, tax_id, notes,
             payment_status, payment_date, remittance_date, createdAt as date
      FROM orderList
      WHERE client = ? AND isDelete = 0
      ORDER BY createdAt DESC
    `,
      [userId],
    )

    // 處理訂單中的產品數據
    for (let order of orders) {
      try {
        order.products = JSON.parse(order.products)
      } catch (e) {
        order.products = []
      }
    }

    res.status(200).json({
      success: true,
      resData: orders,
    })
  } catch (error) {
    console.error('獲取會員訂單時發生錯誤:', error)
    res.status(500).json({ success: false, message: '獲取會員訂單失敗，請稍後再試' })
  }
}

// 獲取會員紅利歷史
const getMemberBonusHistory = async (req, res) => {
  try {
    // 從認證中間件獲取用戶 ID
    const userId = req.user.id

    // 查詢會員紅利歷史
    const bonusHistory = await executeQuery(
      `
      SELECT id, description, points, res, createdAt
      FROM hlList
      WHERE user = ? AND isDelete = 0
      ORDER BY createdAt DESC
    `,
      [userId],
    )

    res.status(200).json({
      success: true,
      resData: bonusHistory,
    })
  } catch (error) {
    console.error('獲取會員紅利歷史時發生錯誤:', error)
    res.status(500).json({ success: false, message: '獲取會員紅利歷史失敗，請稍後再試' })
  }
}

module.exports = {
  getMemberInfo,
  getMemberOrders,
  getMemberBonusHistory,
}
