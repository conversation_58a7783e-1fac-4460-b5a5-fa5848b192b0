const { executeQuery, executeQuerySingle, executeNonQuery } = require('../database/db')
const { v4: uuidv4 } = require('uuid')

// 獲取訂單列表
const getOrderList = async (req, res) => {
  try {
    const { client, id, startDate, endDate, status } = req.query

    let sql = `
      SELECT o.*, u.name as clientName
      FROM orderList o
      JOIN users u ON o.client = u.id
      WHERE o.isDelete = 0
    `
    const params = []

    // 根據客戶ID過濾
    if (client) {
      sql += ' AND o.client = ?'
      params.push(client)
    }

    // 根據訂單ID過濾
    if (id) {
      sql += ' AND o.id = ?'
      params.push(id)
    }

    // 根據日期範圍過濾
    if (startDate) {
      sql += ' AND o.createdAt >= ?'
      params.push(startDate)
    }

    if (endDate) {
      sql += ' AND o.createdAt <= ?'
      params.push(endDate)
    }

    // 根據訂單狀態過濾
    if (status) {
      sql += ' AND o.status = ?'
      params.push(status)
    }

    // 根據創建時間排序（最新的在前）
    sql += ' ORDER BY o.createdAt DESC'

    // 執行查詢
    const orders = await executeQuery(sql, params)

    // 處理產品資料（將JSON字符串轉為對象）
    for (let order of orders) {
      order.products = JSON.parse(order.products)
    }

    // 返回結果
    res.status(200).json({
      success: true,
      resData: orders,
      total: orders.length,
    })
  } catch (error) {
    console.error('獲取訂單列表時發生錯誤:', error)
    res.status(500).json({ success: false, message: '獲取訂單列表失敗，請稍後再試' })
  }
}

// 獲取訂單詳情
const getOrderDetail = async (req, res) => {
  try {
    const { id } = req.params

    // 獲取訂單詳情
    const order = await executeQuerySingle(
      `
      SELECT o.*, u.name as clientName
      FROM orderList o
      JOIN users u ON o.client = u.id
      WHERE o.id = ? AND o.isDelete = 0
    `,
      [id],
    )

    if (!order) {
      return res.status(404).json({ success: false, message: '訂單不存在或已被刪除' })
    }

    // 處理產品資料（將JSON字符串轉為對象）
    order.products = JSON.parse(order.products)

    // 返回訂單詳情
    res.status(200).json({
      success: true,
      body: order,
    })
  } catch (error) {
    console.error('獲取訂單詳情時發生錯誤:', error)
    res.status(500).json({ success: false, message: '獲取訂單詳情失敗，請稍後再試' })
  }
}

// 創建新訂單
const createOrder = async (req, res) => {
  try {
    const {
      client,
      products,
      total,
      orderer,           // 訂購人
      orderer_phone,     // 訂購人電話
      receiver,          // 收貨人
      receiver_phone,    // 收貨人電話
      phone,             // 保持向後相容性
      address,
      detailed_address,
      cooperative,
      invoice_title,
      tax_id,
      notes
    } = req.body

    // 驗證必須欄位 - 更新驗證邏輯
    if (!client || !products || !total || !receiver || !address) {
      return res.status(400).json({
        success: false,
        message: '客戶、產品、總金額、收貨人和地址為必填欄位',
      })
    }

    // 確保有收貨人電話（優先使用 receiver_phone，其次使用 phone）
    const finalReceiverPhone = receiver_phone || phone
    if (!finalReceiverPhone) {
      return res.status(400).json({
        success: false,
        message: '收貨人電話為必填欄位',
      })
    }

    // 確保產品數據是有效的JSON字符串
    let productsJson
    if (typeof products === 'string') {
      try {
        JSON.parse(products)
        productsJson = products
      } catch {
        return res.status(400).json({ success: false, message: '產品數據格式無效' })
      }
    } else if (Array.isArray(products)) {
      productsJson = JSON.stringify(products)
    } else {
      return res.status(400).json({ success: false, message: '產品數據格式無效' })
    }

    // 生成唯一訂單編號 (年月日 + 3位流水號)
    const date = new Date()
    const dateStr = `${date.getFullYear()}${String(date.getMonth() + 1).padStart(2, '0')}${String(date.getDate()).padStart(2, '0')}`

    // 獲取當天最後一筆訂單號
    const lastOrder = await executeQuerySingle(
      `SELECT orderId FROM orderList
       WHERE orderId LIKE ?
       ORDER BY orderId DESC LIMIT 1`,
      [`${dateStr}%`],
    )

    let sequenceNum = 1
    if (lastOrder && lastOrder.orderId) {
      // 從訂單號中提取序列號部分並加1
      const lastSequence = parseInt(lastOrder.orderId.substring(8))
      sequenceNum = isNaN(lastSequence) ? 1 : lastSequence + 1
    }

    // 格式化為3位數，不足位數前面補0
    const sequenceStr = String(sequenceNum).padStart(3, '0')
    const orderId = `${dateStr}${sequenceStr}`

    // 生成訂單創建時間 (使用伺服器當前時間，而不是依賴客戶端提供的時間)
    const now = new Date().toISOString()

    // 插入新訂單，使用伺服器端時間作為 createdAt 和 updatedAt
    const result = await executeNonQuery(
      `INSERT INTO orderList (orderId, client, products, total, orderer, orderer_phone, receiver, receiver_phone, phone, address, detailed_address, cooperative, invoice_title, tax_id, notes, status, createdAt, updatedAt)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        orderId,
        client,
        productsJson,
        total,
        orderer || '',
        orderer_phone || '',
        receiver,
        finalReceiverPhone,
        finalReceiverPhone, // 保持向後相容性
        address,
        detailed_address || '',
        cooperative || '',
        invoice_title || '',
        tax_id || '',
        notes || '',
        1, // 狀態1表示新訂單
        now,
        now
      ]
    )

    // 返回新訂單ID和訂單編號
    res.status(201).json({
      success: true,
      message: '訂單創建成功',
      body: {
        id: result.lastID,
        orderId,
        createdAt: now,
      },
    })
  } catch (error) {
    console.error('創建訂單時發生錯誤:', error)
    res.status(500).json({ success: false, message: '創建訂單失敗，請稍後再試' })
  }
}

// 更新訂單狀態
const updateOrderStatus = async (req, res) => {
  try {
    const { id, status } = req.body

    if (!id || status === undefined) {
      return res.status(400).json({ success: false, message: '訂單ID和狀態為必填欄位' })
    }

    // 檢查訂單是否存在
    const order = await executeQuerySingle(
      'SELECT * FROM orderList WHERE id = ? AND isDelete = 0',
      [id],
    )
    if (!order) {
      return res.status(404).json({ success: false, message: '訂單不存在或已被刪除' })
    }

    // 更新訂單狀態
    await executeNonQuery('UPDATE orderList SET status = ?, updatedAt = ? WHERE id = ?', [
      status,
      new Date().toISOString(),
      id,
    ])

    res.status(200).json({
      success: true,
      message: '訂單狀態已更新',
      status: 200,
    })
  } catch (error) {
    console.error('更新訂單狀態時發生錯誤:', error)
    res.status(500).json({ success: false, message: '更新訂單狀態失敗，請稍後再試' })
  }
}

// 更新訂單已讀狀態
const updateOrderRead = async (req, res) => {
  try {
    const { id, isRead } = req.body

    if (!id || isRead === undefined) {
      return res.status(400).json({ success: false, message: '訂單ID和已讀狀態為必填欄位' })
    }

    // 檢查訂單是否存在
    const order = await executeQuerySingle(
      'SELECT * FROM orderList WHERE id = ? AND isDelete = 0',
      [id],
    )
    if (!order) {
      return res.status(404).json({ success: false, message: '訂單不存在或已被刪除' })
    }

    // 更新訂單已讀狀態
    await executeNonQuery('UPDATE orderList SET isRead = ?, updatedAt = ? WHERE id = ?', [
      isRead,
      new Date().toISOString(),
      id,
    ])

    res.status(200).json({
      success: true,
      message: '訂單已讀狀態已更新',
      status: 200,
    })
  } catch (error) {
    console.error('更新訂單已讀狀態時發生錯誤:', error)
    res.status(500).json({ success: false, message: '更新訂單已讀狀態失敗，請稍後再試' })
  }
}

// 刪除訂單（軟刪除）
const deleteOrder = async (req, res) => {
  try {
    const { id } = req.query

    if (!id) {
      return res.status(400).json({ success: false, message: '未提供訂單ID' })
    }

    // 檢查訂單是否存在
    const order = await executeQuerySingle(
      'SELECT * FROM orderList WHERE id = ? AND isDelete = 0',
      [id],
    )
    if (!order) {
      return res.status(404).json({ success: false, message: '訂單不存在或已被刪除' })
    }

    // 軟刪除訂單
    await executeNonQuery('UPDATE orderList SET isDelete = 1, updatedAt = ? WHERE id = ?', [
      new Date().toISOString(),
      id,
    ])

    res.status(200).json({
      success: true,
      message: '訂單已刪除',
    })
  } catch (error) {
    console.error('刪除訂單時發生錯誤:', error)
    res.status(500).json({ success: false, message: '刪除訂單失敗，請稍後再試' })
  }
}

module.exports = {
  getOrderList,
  getOrderDetail,
  createOrder,
  updateOrderStatus,
  updateOrderRead,
  deleteOrder,
}
