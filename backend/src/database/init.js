const Database = require('better-sqlite3')
const path = require('path')
const fs = require('fs')
const dotenv = require('dotenv')

// 載入環境變數
dotenv.config({ path: path.join(__dirname, '../../.env') })

// 獲取資料庫路徑
const dbPath = process.env.DB_PATH || path.join(__dirname, 'culroc.db')

// 確保資料庫目錄存在
const dbDir = path.dirname(dbPath)
if (!fs.existsSync(dbDir)) {
  fs.mkdirSync(dbDir, { recursive: true })
}

// 初始化資料庫
function initDb() {
  const db = new Database(dbPath, { verbose: console.log })

  // 啟用外鍵約束
  db.pragma('foreign_keys = ON')

  // 創建 users 表
  db.exec(`
    CREATE TABLE IF NOT EXISTS users (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      userId TEXT UNIQUE,
      name TEXT NOT NULL,
      mail TEXT UNIQUE NOT NULL,
      password TEXT NOT NULL,
      phone TEXT,
      address TEXT,
      birthday TEXT,
      gender TEXT,
      bonusPoints INTEGER DEFAULT 0,
      role TEXT DEFAULT 'user',
      token TEXT,
      isDelete INTEGER DEFAULT 0,
      createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `)

  // 創建 products 表
  db.exec(`
    CREATE TABLE IF NOT EXISTS products (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL,
      description TEXT,
      price1 INTEGER NOT NULL,
      price2 INTEGER,
      stock INTEGER DEFAULT 0,
      images TEXT,
      sort TEXT,
      isDelete INTEGER DEFAULT 0,
      createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `)

  // 創建 orderList 表
  db.exec(`
    CREATE TABLE IF NOT EXISTS orderList (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      orderId TEXT UNIQUE NOT NULL,
      client INTEGER NOT NULL,
      products TEXT NOT NULL,
      total INTEGER NOT NULL,
      orderer TEXT,
      orderer_phone TEXT,
      receiver TEXT NOT NULL,
      receiver_phone TEXT,
      phone TEXT NOT NULL,
      address TEXT NOT NULL,
      detailed_address TEXT,
      cooperative TEXT,
      invoice_title TEXT,
      tax_id TEXT,
      notes TEXT,
      status INTEGER DEFAULT 1,
      payment_status INTEGER DEFAULT 0,
      payment_date DATETIME,
      remittance_date DATETIME,
      isRead INTEGER DEFAULT 0,
      isDelete INTEGER DEFAULT 0,
      createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (client) REFERENCES users (id)
    )
  `)

  // 檢查並添加缺少的欄位（用於現有數據庫升級）
  try {
    // 檢查所有欄位
    const tableInfo = db.prepare('PRAGMA table_info(orderList)').all()
    const hasOrderer = tableInfo.some(col => col.name === 'orderer')
    const hasOrdererPhone = tableInfo.some(col => col.name === 'orderer_phone')
    const hasReceiverPhone = tableInfo.some(col => col.name === 'receiver_phone')
    const hasDetailedAddress = tableInfo.some(col => col.name === 'detailed_address')
    const hasCooperative = tableInfo.some(col => col.name === 'cooperative')
    const hasInvoiceTitle = tableInfo.some(col => col.name === 'invoice_title')
    const hasTaxId = tableInfo.some(col => col.name === 'tax_id')
    const hasNotes = tableInfo.some(col => col.name === 'notes')
    const hasPaymentStatus = tableInfo.some(col => col.name === 'payment_status')
    const hasPaymentDate = tableInfo.some(col => col.name === 'payment_date')
    const hasRemittanceDate = tableInfo.some(col => col.name === 'remittance_date')

    if (!hasOrderer) {
      db.exec('ALTER TABLE orderList ADD COLUMN orderer TEXT')
      console.log('已添加 orderer 欄位到 orderList 表')
    }
    if (!hasOrdererPhone) {
      db.exec('ALTER TABLE orderList ADD COLUMN orderer_phone TEXT')
      console.log('已添加 orderer_phone 欄位到 orderList 表')
    }
    if (!hasReceiverPhone) {
      db.exec('ALTER TABLE orderList ADD COLUMN receiver_phone TEXT')
      console.log('已添加 receiver_phone 欄位到 orderList 表')
    }
    if (!hasDetailedAddress) {
      db.exec('ALTER TABLE orderList ADD COLUMN detailed_address TEXT')
      console.log('已添加 detailed_address 欄位到 orderList 表')
    }
    if (!hasCooperative) {
      db.exec('ALTER TABLE orderList ADD COLUMN cooperative TEXT')
      console.log('已添加 cooperative 欄位到 orderList 表')
    }
    if (!hasInvoiceTitle) {
      db.exec('ALTER TABLE orderList ADD COLUMN invoice_title TEXT')
      console.log('已添加 invoice_title 欄位到 orderList 表')
    }
    if (!hasTaxId) {
      db.exec('ALTER TABLE orderList ADD COLUMN tax_id TEXT')
      console.log('已添加 tax_id 欄位到 orderList 表')
    }
    if (!hasNotes) {
      db.exec('ALTER TABLE orderList ADD COLUMN notes TEXT')
      console.log('已添加 notes 欄位到 orderList 表')
    }
    if (!hasPaymentStatus) {
      db.exec('ALTER TABLE orderList ADD COLUMN payment_status INTEGER DEFAULT 0')
      console.log('已添加 payment_status 欄位到 orderList 表')
    }
    if (!hasPaymentDate) {
      db.exec('ALTER TABLE orderList ADD COLUMN payment_date DATETIME')
      console.log('已添加 payment_date 欄位到 orderList 表')
    }
    if (!hasRemittanceDate) {
      db.exec('ALTER TABLE orderList ADD COLUMN remittance_date DATETIME')
      console.log('已添加 remittance_date 欄位到 orderList 表')
    }
  } catch (error) {
    console.log('檢查或添加 orderList 欄位時發生錯誤:', error.message)
  }

  // 創建 hlList (紅利清單) 表
  db.exec(`
    CREATE TABLE IF NOT EXISTS hlList (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user INTEGER NOT NULL,
      description TEXT,
      points INTEGER NOT NULL,
      res INTEGER NOT NULL,
      isDelete INTEGER DEFAULT 0,
      createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (user) REFERENCES users (id)
    )
  `)

  // 創建留言表
  db.exec(`
    CREATE TABLE IF NOT EXISTS comments (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user INTEGER NOT NULL,
      product INTEGER DEFAULT NULL,
      content TEXT NOT NULL,
      isRead INTEGER DEFAULT 0,
      isDelete INTEGER DEFAULT 0,
      createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (user) REFERENCES users (id),
      FOREIGN KEY (product) REFERENCES products (id)
    )
  `)

  // 創建基本信息表
  db.exec(`
    CREATE TABLE IF NOT EXISTS basic (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      siteName TEXT,
      logo TEXT,
      phone TEXT,
      email TEXT,
      address TEXT,
      facebook TEXT,
      instagram TEXT,
      line TEXT,
      shopNotice TEXT,
      shippingPolicy TEXT,
      metaDescription TEXT
    )
  `)

  // 創建商品分類表
  db.exec(`
    CREATE TABLE IF NOT EXISTS categories (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL,
      parent_id INTEGER DEFAULT NULL,
      sort_order INTEGER DEFAULT 0,
      description TEXT,
      createdAt TEXT DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (parent_id) REFERENCES categories (id)
    )
  `)

  // 創建輪播圖表
  db.exec(`
    CREATE TABLE IF NOT EXISTS carousel (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      image_url TEXT NOT NULL,
      title TEXT,
      description TEXT,
      link TEXT,
      is_active INTEGER DEFAULT 1,
      sort_order INTEGER DEFAULT 0,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `)

  // 創建品牌表
  db.exec(`
    CREATE TABLE IF NOT EXISTS brands (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL,
      description TEXT,
      logo TEXT,
      website TEXT,
      isDelete INTEGER DEFAULT 0,
      createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `)

  // 檢查 basic 表是否為空，如果為空則插入初始數據
  const basicCount = db.prepare('SELECT COUNT(*) as count FROM basic').get()

  if (basicCount.count === 0) {
    const stmt = db.prepare(`
      INSERT INTO basic (siteName, logo, phone, email, address, facebook, instagram, line)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `)

    stmt.run(
          'Culroc商城',
          'logo.png',
          '0123456789',
          '<EMAIL>',
          '臺灣臺北市',
      'https://facebook.com/culroc',
      'https://instagram.com/culroc',
      'https://line.me/culroc'
    )

            console.log('已插入基本網站資訊')
          }

  console.log('已成功連接到 SQLite 資料庫')
  return db
}

module.exports = initDb
