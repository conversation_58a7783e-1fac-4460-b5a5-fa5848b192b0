<script setup>
import { ref, defineProps, onMounted, reactive, computed } from 'vue'
import CartItem from '../components/CartItem.vue'
import TwDoptions from '@/components/utils/TwDoptions.vue'
import api from '@/utils/api.js'
import { message } from 'ant-design-vue'
import _ from 'lodash'
import { useRouter } from 'vue-router'
import { storeToRefs } from 'pinia'
import piniaStores from '@/stores/apiStore'
import dayjs from 'dayjs'
import ChatMessageList from '@/components/ChatMessageList.vue'
const router = useRouter()
const handelPinia = piniaStores()
const { clientData, isUser, carts } = storeToRefs(handelPinia)
const {
  patchApiData,
  getApiData,
  delApiData,
  cancelRequests,
  postApiData,
  checkAuthToken,
  logApiError,
} = api()

const productData = [
  {
    id: 651,
    name: '沒麥乾拌糆-綠椰咖',

    price1: 700,
    price2: 600,
  },
  {
    id: 65851,
    name: '糙米鬆餅預拌粉',

    price1: 700,
    price2: 500,
  },
]

const status = ref('order')
const inputClient = ref({})
const orders = ref(null)
const orderInfo = reactive({
  open: false,
  data: {},
})

// 訂單分頁相關
const pagination = reactive({
  current: 1,
  pageSize: 5,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  pageSizeOptions: ['5', '10', '20', '50'],
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 項，共 ${total} 筆訂單`
})

// 計算當前頁面顯示的訂單
const paginatedOrders = computed(() => {
  if (!orders.value || !Array.isArray(orders.value)) {
    return []
  }
  
  const start = (pagination.current - 1) * pagination.pageSize
  const end = start + pagination.pageSize
  return orders.value.slice(start, end)
})

// 處理分頁變化
const handlePageChange = (page, pageSize) => {
  pagination.current = page
  pagination.pageSize = pageSize
}

// 處理每頁顯示數量變化
const handleShowSizeChange = (current, size) => {
  pagination.current = 1
  pagination.pageSize = size
}

// 留言相關
const userComments = ref([])
const loadingComments = ref(false)

// 表單驗證相關
const formErrors = reactive({
  phone: ''
})

// 電話號碼驗證函數
const validatePhone = (phone) => {
  if (!phone) {
    return '聯絡電話為必填項目'
  }
  
  // 移除所有空格和特殊字符，只保留數字和+號
  const cleanPhone = phone.replace(/[^\d+]/g, '')
  
  // 台灣手機號碼格式驗證 (09xxxxxxxx)
  const taiwanMobileRegex = /^09\d{8}$/
  // 台灣市話格式驗證 (0x-xxxxxxxx 或 0x-xxxxxxx)
  const taiwanLandlineRegex = /^0[2-8]-?\d{7,8}$/
  // 國際號碼格式 (+886xxxxxxxxx)
  const internationalRegex = /^\+886\d{9}$/
  
  if (taiwanMobileRegex.test(cleanPhone) || 
      taiwanLandlineRegex.test(phone) || 
      internationalRegex.test(cleanPhone)) {
    return ''
  }
  
  return '請輸入正確的電話號碼格式（例：0912345678 或 02-12345678）'
}

// 即時驗證電話號碼
const validatePhoneInput = () => {
  formErrors.phone = validatePhone(inputClient.value.phone)
}

const fetchUserComments = async () => {
  loadingComments.value = true

  // 檢查身份驗證令牌
  if (!checkAuthToken()) {
    loadingComments.value = false
    return
  }

  try {
    console.log('正在獲取用戶留言...')
    const res = await getApiData('user/comments')
    console.log('獲取留言響應:', res)
    if (res && res.resData) {
      userComments.value = res.resData
      console.log('成功獲取用戶留言:', userComments.value.length, '條')
      // 輸出留言結構以便調試
      if (userComments.value.length > 0) {
        console.log('留言示例:', userComments.value[0])
      }
    } else {
      console.error('獲取留言失敗:', res?.error)
    }
  } catch (error) {
    logApiError('user/comments', error)
  } finally {
    loadingComments.value = false
  }
}

const deleteComment = async (commentId) => {
  try {
    console.log('正在刪除留言 ID:', commentId)
    const res = await delApiData(`comments/${commentId}`)
    console.log('刪除留言響應:', res)
    
    if (res && res.status === 200) {
      // 立即從本地數組移除該留言
      userComments.value = userComments.value.filter(comment => comment.id !== commentId)
      message.success('留言刪除成功')
      
      // 重新載入留言以確保數據同步
      setTimeout(() => {
        fetchUserComments()
      }, 500)
    } else {
      console.error('刪除留言失敗:', res)
      message.error('留言刪除失敗，請稍後再試')
    }
  } catch (error) {
    console.error('刪除留言時發生錯誤:', error)
    message.error('刪除留言失敗，請稍後再試')
  }
}

// 新增留言功能
const commentModalVisible = ref(false)
const commentContent = ref('')
const commentSubmitting = ref(false)

const openCommentModal = () => {
  commentModalVisible.value = true
  commentContent.value = ''
}

const submitComment = async () => {
  if (!commentContent.value.trim()) {
    message.warning('留言內容不能為空')
    return
  }

  commentSubmitting.value = true

  try {
    const res = await postApiData('comments', {
      content: commentContent.value,
    })

    if (res && res.status === 201) {
      message.success('留言發送成功')
      commentModalVisible.value = false
      commentContent.value = ''
      // 重新載入留言
      fetchUserComments()
    } else {
      message.error(res?.error || '留言發送失敗，請稍後再試')
    }
  } catch (error) {
    console.error('提交留言時發生錯誤:', error)
    message.error('留言發送失敗，請稍後再試')
  } finally {
    commentSubmitting.value = false
  }
}

onMounted(async () => {
  // 檢查用戶是否已登入
  if (!isUser.value) {
    console.log('用戶未登入，重定向到登入頁面')
    // message.warning('請先登入')
    
    // 保存當前頁面URL，以便登入後返回
    localStorage.setItem('redirect_after_login', window.location.href)
    
    router.push('/login')
    return
  }

  // 根據URL查詢參數切換頁籤
  const tab = router.currentRoute.value.query.tab
  if (tab) {
    if (tab === 'userData') {
      status.value = 'userData'
    } else if (tab === 'comments') {
      status.value = 'comments'
      fetchUserComments()
    } else {
      status.value = 'order'
    }
  }

  // 初始化用戶資料
  inputClient.value = { ...clientData.value }
  
  // 詳細的除錯資訊
  console.log('=== 開始獲取用戶訂單資料 ===')
  console.log('用戶登入狀態:', isUser.value)
  console.log('用戶資料:', clientData.value)
  console.log('用戶ID:', clientData.value?.id)
  console.log('Local Storage Token:', localStorage.getItem('token') ? 'Token 存在' : 'Token 不存在')
  
  if (clientData.value && clientData.value.id) {
    try {
      console.log('準備呼叫會員訂單API...')
      
      // 使用會員訂單API獲取訂單數據
      const res = await getApiData('member/orders')
      console.log('會員訂單API完整響應:', res)
      
      // 檢查響應結構
      console.log('響應中的 success:', res?.success)
      console.log('響應中的 resData:', res?.resData)
      console.log('響應中的 resTotal:', res?.resTotal)
      console.log('響應中的 resTotalMoney:', res?.resTotalMoney)

      if (res && res.resData && Array.isArray(res.resData)) {
        // 輸出詳細的訂單數據以便調試
        console.log('訂單API原始數據樣本:', res.resData[0] || {})
        console.log('訂單API數據結構:', Object.keys(res.resData[0] || {}))
        
        // 確保 resData 是數組
        const orderData = Array.isArray(res.resData) ? res.resData : [res.resData]

        // 處理每個訂單的產品數據
        orders.value = orderData.map(order => processOrderData(order))
        
        // 更新分頁總數
        pagination.total = orders.value.length

        console.log('處理後的訂單數據樣本:', orders.value[0] || {})
        console.log('獲取到訂單數據:', orders.value.length, '筆')
      } else if (res && res.resData && !Array.isArray(res.resData)) {
        // 處理非陣列的情況
        console.log('API 返回的不是陣列，嘗試包裝為陣列')
        orders.value = [processOrderData(res.resData)]
        
        // 更新分頁總數
        pagination.total = orders.value.length
      } else {
        console.error('獲取訂單數據失敗或數據格式不正確:', {
          resData: res?.resData,
          resTotal: res?.resTotal,
          resTotalMoney: res?.resTotalMoney,
          success: res?.success,
          error: res?.error
        })
        orders.value = []
        pagination.total = 0
        
        // 如果是認證錯誤，提示用戶重新登入
        if (res?.error && res.error.includes('401')) {
          message.warning('登入已過期，請重新登入')
          router.push('/login')
        }
      }
    } catch (error) {
      console.error('獲取訂單數據出錯:', error)
      console.error('錯誤詳細資訊:', {
        message: error.message,
        response: error.response?.data,
        status: error.response?.status
      })
      
      orders.value = []
      pagination.total = 0
      
      // 根據錯誤類型提供更具體的錯誤處理
      if (error.response?.status === 401) {
        message.warning('登入已過期，請重新登入')
        router.push('/login')
      } else if (error.response?.status === 500) {
        message.error('伺服器錯誤，請稍後再試')
      } else {
        message.error('獲取訂單資料失敗，請稍後再試')
      }
    }
  } else {
    console.error('用戶未登入或用戶ID未定義，無法獲取訂單')
    orders.value = []
    pagination.total = 0
    
    // 如果沒有用戶資料，重新導向到登入頁面
    message.warning('請先登入')
    router.push('/login')
  }
  
  console.log('=== 訂單資料獲取完成 ===')
})

// 處理訂單數據，確保所有屬性都有合理值
const processOrderData = (order) => {
  if (!order) return order

  // 複製訂單數據以避免修改原始數據
  const processedOrder = { ...order }

  // 確保 products 是數組
  if (processedOrder.products) {
    if (typeof processedOrder.products === 'string') {
      try {
        processedOrder.products = JSON.parse(processedOrder.products)
      } catch (e) {
        console.error('解析訂單產品數據失敗:', e)
        processedOrder.products = []
      }
    } else if (!Array.isArray(processedOrder.products)) {
      processedOrder.products = []
    }
  } else {
    processedOrder.products = []
  }

  // 確保基本屬性都有值
  processedOrder.receiver = processedOrder.receiver || ''
  processedOrder.phone = processedOrder.phone || ''
  processedOrder.address = processedOrder.address || '自取/無須配送'
  processedOrder.status = processedOrder.status || 1
  processedOrder.total = processedOrder.total || 0

  // 處理日期
  if (!processedOrder.date && !processedOrder.createdAt) {
    processedOrder.createdAt = new Date().toISOString()
  }

  return processedOrder
}

const openOrder = (i) => {
  // 複製訂單數據，避免修改原始數據
  const orderData = { ...i };
  
  // 處理訂單資料
  if (typeof orderData.products === 'string') {
    try {
      orderData.products = JSON.parse(orderData.products);
    } catch (e) {
      console.error('解析訂單產品數據失敗:', e);
      orderData.products = [];
    }
  }
  
  // 確保基本屬性存在，避免undefined錯誤
  orderData.receiver = orderData.receiver || '';
  orderData.phone = orderData.phone || '';
  orderData.address = orderData.address || '自取/無須配送';
  
  // 輸出除錯信息
  console.log('訂單詳情 - 訂購人:', orderData.receiver);
  console.log('訂單詳情 - 聯絡電話:', orderData.phone);
  
  // 更新訂單資料並打開彈窗
  orderInfo.data = orderData;
  orderInfo.open = true;
}

const cancelOrder = async () => {
  await patchApiData('orderList', { status: 4, id: orderInfo.data.id })
  const index = _.findIndex(orders.value, (i) => orderInfo.data.id == i.id)
  orders.value[index].status = 4
  orderInfo.open = false
}

const sendClient = _.debounce(async () => {
  // 驗證表單
  formErrors.phone = validatePhone(inputClient.value.phone)
  
  // 檢查是否有驗證錯誤
  if (formErrors.phone) {
    message.error('請修正表單錯誤後再提交')
    return
  }
  
  console.log(_.isEqual(inputClient.value, clientData.value))
  const diff = _.omitBy(inputClient.value, (value, key) => _.isEqual(value, clientData.value[key]))
  console.log('diff', diff)

  if (Object.keys(diff).length === 0) {
    message.info('沒有需要更新的資料')
    return
  }

  try {
    const res = await patchApiData('user', { ...diff, id: clientData.value.id })
    if (res && res.status === 200) {
      message.success('修改成功')
      clientData.value = inputClient.value

      // 修改成功後導航到首頁
      setTimeout(() => {
        window.location.href = '/'
      }, 1000) // 延遲1秒，讓用戶可以看到成功訊息
    } else {
      message.error(res?.error || '發生錯誤，請稍後再試')
    }
  } catch (error) {
    console.error('更新資料時發生錯誤:', error)
    message.error('更新資料發生錯誤，請稍後再試')
  }
}, 500)

// 獲取付款狀態文字
const getPaymentStatusText = (status) => {
  return status === 1 ? '已付款' : '待付款'
}

// 獲取付款狀態顏色
const getPaymentStatusColor = (status) => {
  return status === 1 ? 'green' : 'orange'
}

const getText = (num) => {
  num = parseInt(num)

  switch (num) {
    case 1:
      return '處理中'
    case 2:
      return '已出貨'
    case 3:
      return '已完成'
    case 4:
      return '已取消'
    default:
      return '未知狀態'
  }
}

const logOut = () => {
  // 清除所有localStorage數據
  localStorage.clear()
  
  // 重置Pinia Store狀態
  isUser.value = false
  clientData.value = {}
  
  // 清空購物車
  handelPinia.clearCart()
  
  // 重新載入頁面以更新所有組件狀態
  window.location.href = '/'
}

// 格式化日期時間為台灣時間
const formatDateTime = (dateString) => {
  if (!dateString) return '-'

  // 處理不同的日期格式
  let date
  if (typeof dateString === 'string') {
    // 如果是 ISO 字符串，直接解析
    if (dateString.includes('T') || dateString.includes('Z')) {
      date = dayjs(dateString)
    } else {
      // 如果是其他格式，嘗試解析
      date = dayjs(dateString)
    }
  } else {
    date = dayjs(dateString)
  }

  // 確保日期有效
  if (!date.isValid()) {
    return dateString // 如果無法解析，返回原始字符串
  }

  // 格式化為台灣時間格式：2025年5月24日 下午5:17
  return date.format('YYYY年M月D日 A h:mm')
}

const changeTab = (tab) => {
  status.value = tab
  router.push({ query: { tab } })
  if (tab === 'comments') {
    fetchUserComments()
  }
}

// 將留言和回覆分開，按時間排序
const sortedMessages = computed(() => {
  if (!userComments.value) return []
  
  const allMessages = []
  
  // 添加用戶留言
  userComments.value.forEach(msg => {
    allMessages.push({
      id: `user-${msg.id}`,
      type: 'user',
      content: msg.content,
      time: new Date(msg.createdAt),
      originalId: msg.id,
      // 添加留言類型信息
      commentType: msg.product ? '商品留言' : '一般留言',
      product: msg.productName || null,
      productId: msg.product || null
    })
    
    // 添加管理員回覆（如果有）
    if (msg.reply) {
      allMessages.push({
        id: `admin-${msg.id}`,
        type: 'admin',
        content: msg.reply,
        time: new Date(msg.replyAt || msg.updatedAt || msg.createdAt), // 使用回覆時間，如果沒有則用更新時間或創建時間
        originalId: msg.id,
        // 管理員回覆繼承原留言的類型
        commentType: msg.product ? '商品留言' : '一般留言',
        product: msg.productName || null,
        productId: msg.product || null
      })
    }
  })
  
  // 按時間排序
  return allMessages.sort((a, b) => a.time - b.time)
})

const newMessage = ref('')

const sendNewMessage = async () => {
  if (!newMessage.value.trim()) return
  
  try {
    const res = await postApiData('comments', {
      content: newMessage.value,
    })

    if (res && res.status === 201) {
      // 清空輸入框
      newMessage.value = ''
      // 重新載入留言
      fetchUserComments()
    }
  } catch (error) {
    console.error('提交留言時發生錯誤:', error)
    message.error('留言發送失敗，請稍後再試')
  }
}

// 刪除整個聊天窗（所有留言）
const deleteChatWindow = async (group) => {
  // 收集該聊天窗所有留言 id
  const ids = group.messages.map(m => m.id)
  for (const id of ids) {
    await delApiData(`comments/${id}`)
  }
  // 重新載入留言
  fetchUserComments()
  // 若刪除的是當前選取，則清空選取
  if (selectedChatKey.value === group.key) {
    selectedChatKey.value = null
  }
}
</script>

<template>
  <!-- <div class="mt-10">
    client: <br>{{ inputClient }}
    <br>
    clientData: <br> {{ clientData }}
  </div> -->
  <div class="container m-auto">
    <div class="flex w-full md:w-2/3 lg:w-1/2 m-auto justify-center items-center my-10 gap-4">
      <a-button @click="changeTab('order')" size="large" class="flex-1 font-semibold text-base"
        >我的訂單</a-button
      >
      <a-button @click="changeTab('userData')" size="large" class="flex-1 font-semibold text-base"
        >個人資料</a-button
      >
      <a-button @click="changeTab('comments')" size="large" class="flex-1 font-semibold text-base">
        留言板
      </a-button>
    </div>

    <div>
      <div v-show="status == 'order'">
        <div class="block md:hidden px-5">
          <template v-if="orders && orders.length > 0">
            <div
              class="border-2 w-full p-4 mb-4"
              v-for="i in paginatedOrders"
              :key="i.id"
              @click="openOrder(i)"
            >
              <p><span class="mr-4">訂單時間</span>{{ formatDateTime(i.date || i.createdAt) }}</p>
              <p><span class="mr-4">訂單編號</span>{{ i.orderId }}</p>
              <p><span class="mr-4">商品數量</span>{{ i.products ? i.products.length : 0 }}</p>
              <p class="text-nowrap">
                <span class="mr-4">購買金額</span>NT <TwDoptions :number="i.total"></TwDoptions>
              </p>
              <p>
                <span class="mr-4">付款狀態</span>
                <a-tag :color="getPaymentStatusColor(i.payment_status)">
                  {{ getPaymentStatusText(i.payment_status) }}
                </a-tag>
              </p>
              <p>
                <span class="mr-4">訂單狀態</span>
                <a-tag :color="i.status == 1 ? 'red' : i.status == 2 ? 'green' : i.status == 3 ? 'blue' : 'gray'">
                  {{ i.status == 1 ? '處理中' : i.status == 2 ? '已出貨' : i.status == 3 ? '已完成' : '已取消' }}
                </a-tag>
              </p>
            </div>
          </template>
          <template v-else>
            <div class="text-center py-8 border rounded-lg">
              <p class="text-gray-500">您目前沒有任何訂單</p>
            </div>
          </template>
        </div>

        <div class="hidden md:block">
          <table class="w-full">
            <!-- <caption>
      Front-end web developer course 2021
    </caption> -->
            <thead>
              <tr>
                <th scope="col">購買日期</th>
                <th scope="col">訂單編號</th>
                <th scope="col" class="">商品數量</th>
                <th scope="col" class="">購買金額</th>
                <th scope="col">付款狀態</th>
                <th scope="col">訂單狀態</th>
                <!-- <th scope="col">小計</th> -->
              </tr>
            </thead>
            <tbody>
              <template v-if="orders && orders.length > 0">
                <tr
                  v-for="i in paginatedOrders"
                  :key="i.id"
                  class="hover:bg-slate-100 cursor-pointer"
                  @click="openOrder(i)"
                >
                  <td class="text-center">
                    <p>{{ formatDateTime(i.date || i.createdAt) }}</p>
                  </td>

                  <td class="text-center">
                    <p>{{ i.orderId }}</p>
                  </td>

                  <td class="">
                    <p class="text-center">{{ i.products ? i.products.length : 0 }}</p>
                  </td>

                  <td class="text-center">
                    <p class="text-nowrap">NT <TwDoptions :number="i.total"></TwDoptions></p>
                  </td>
                  
                  <td class="text-center">
                    <a-tag :color="getPaymentStatusColor(i.payment_status)">
                      {{ getPaymentStatusText(i.payment_status) }}
                    </a-tag>
                  </td>
                  
                  <td class="text-center">
                    <a-tag :color="i.status == 1 ? 'red' : i.status == 2 ? 'green' : i.status == 3 ? 'blue' : 'gray'">
                      {{ i.status == 1 ? '處理中' : i.status == 2 ? '已出貨' : i.status == 3 ? '已完成' : '已取消' }}
                    </a-tag>
                  </td>
                </tr>
              </template>
              <template v-else>
                <tr>
                  <td colspan="6" class="text-center py-8">
                    <p class="text-gray-500">您目前沒有任何訂單</p>
                  </td>
                </tr>
              </template>
            </tbody>
            <!-- <tfoot>
              <tr>
                <th scope="row" colspan="4" class="text-right">總計</th>
                <td class="text-right text-nowrap">NT <TwDoptions :number="isUser ? total : total"></TwDoptions>
                </td>
              </tr>
            </tfoot> -->
          </table>
        </div>

        <a-modal
          v-model:open="orderInfo.open"
          :title="orderInfo.data.orderId + '訂單內容'"
          :footer="null"
          :width="'1200px'"
        >
          <CartItem
            :cartData="orderInfo.data.products"
            :orderTotal="orderInfo.data.total"
          ></CartItem>

          <a-descriptions title="訂單資料">
            <a-descriptions-item label="訂購時間">{{
              formatDateTime(orderInfo.data.date || orderInfo.data.createdAt)
            }}</a-descriptions-item>
            <a-descriptions-item label="訂購人">
              {{ orderInfo.data.orderer || orderInfo.data.receiver || '未提供' }}
              <span v-if="!orderInfo.data.orderer && !orderInfo.data.receiver" class="text-red-500 text-xs ml-1">(資料未填寫)</span>
            </a-descriptions-item>
            <a-descriptions-item label="訂購人電話">
              {{ orderInfo.data.orderer_phone || orderInfo.data.phone || '未提供' }}
              <span v-if="!orderInfo.data.orderer_phone && !orderInfo.data.phone" class="text-red-500 text-xs ml-1">(資料未填寫)</span>
            </a-descriptions-item>
            <a-descriptions-item label="收貨人">
              {{ orderInfo.data.receiver || '未提供' }}
              <span v-if="!orderInfo.data.receiver" class="text-red-500 text-xs ml-1">(資料未填寫)</span>
            </a-descriptions-item>
            <a-descriptions-item label="收貨人電話">
              {{ orderInfo.data.receiver_phone || orderInfo.data.phone || '未提供' }}
              <span v-if="!orderInfo.data.receiver_phone && !orderInfo.data.phone" class="text-red-500 text-xs ml-1">(資料未填寫)</span>
            </a-descriptions-item>
            <a-descriptions-item label="住址（詳細地址）">
              {{ orderInfo.data.detailed_address && orderInfo.data.detailed_address !== '/' ? orderInfo.data.detailed_address : '未提供' }}
              <span v-if="!orderInfo.data.detailed_address || orderInfo.data.detailed_address === '/'" class="text-gray-400 text-xs ml-1">(可選填)</span>
            </a-descriptions-item>
            <a-descriptions-item label="儲互社">
              {{ orderInfo.data.cooperative && orderInfo.data.cooperative !== '/' ? orderInfo.data.cooperative : '未提供' }}
              <span v-if="!orderInfo.data.cooperative || orderInfo.data.cooperative === '/'" class="text-gray-400 text-xs ml-1">(可選填)</span>
            </a-descriptions-item>
            <a-descriptions-item label="公司抬頭">
              {{ orderInfo.data.invoice_title && orderInfo.data.invoice_title !== '/' ? orderInfo.data.invoice_title : '未提供' }}
              <span v-if="!orderInfo.data.invoice_title || orderInfo.data.invoice_title === '/'" class="text-gray-400 text-xs ml-1">(可選填)</span>
            </a-descriptions-item>
            <a-descriptions-item label="統編">
              {{ orderInfo.data.tax_id && orderInfo.data.tax_id !== '/' ? orderInfo.data.tax_id : '未提供' }}
              <span v-if="!orderInfo.data.tax_id || orderInfo.data.tax_id === '/'" class="text-gray-400 text-xs ml-1">(可選填)</span>
            </a-descriptions-item>
            <a-descriptions-item label="備註">
              {{ orderInfo.data.notes && orderInfo.data.notes !== '/' ? orderInfo.data.notes : '無' }}
              <span v-if="!orderInfo.data.notes || orderInfo.data.notes === '/'" class="text-gray-400 text-xs ml-1">(可選填)</span>
            </a-descriptions-item>
            <a-descriptions-item label="付款狀態">
              <a-tag :color="getPaymentStatusColor(orderInfo.data.payment_status)">
                {{ getPaymentStatusText(orderInfo.data.payment_status) }}
              </a-tag>
              <p v-if="orderInfo.data.payment_status === 1 && orderInfo.data.payment_date" class="text-sm text-gray-500 mt-1">
                付款時間：{{ formatDateTime(orderInfo.data.payment_date) }}
              </p>
            </a-descriptions-item>
            <a-descriptions-item label="訂單狀態">
              <!-- {{ getText(orderInfo.data.status) }} -->
              <a-tag
                :color="
                  orderInfo.data.status == 1 ? 'red' : orderInfo.data.status == 2 ? 'green' : orderInfo.data.status == 3 ? 'blue' : 'gray'
                "
              >
                {{
                  orderInfo.data.status == 1
                    ? '處理中'
                    : orderInfo.data.status == 2
                      ? '已出貨'
                      : orderInfo.data.status == 3
                        ? '已完成'
                        : '已取消'
                }}</a-tag
              >
            </a-descriptions-item>
          </a-descriptions>

          <div class="flex justify-end">
            <a-popconfirm
              title="確定取消訂單嗎？"
              ok-text="確定"
              cancel-text="取消"
              @confirm="cancelOrder"
              class="mt-1"
            >
              <button
                type="button"
                class="bg-red-500 text-white w-1/6 p-2 mt-4"
                v-if="orderInfo.data.status == 1"
              >
                取消訂單
              </button>
            </a-popconfirm>
          </div>
        </a-modal>
      </div>
      <div v-show="status == 'userData'">
        <form @submit.prevent="sendClient" class="grid grid-cols-2 gap-4 w-1/2 m-auto">
          <div class="col-span-2 md:col-span-1">
            <!-- <label for="password">Password:</label> -->
            <p>姓名</p>
            <input
              class="w-full border-2 border-black"
              type="text"
              placeholder="請輸入姓名"
              v-model="inputClient.name"
            />
          </div>
          <div class="col-span-2 md:col-span-1">
            <!-- <label for="username">Username:</label> -->
            <p>聯絡電話</p>
            <input
              :class="[
                'w-full border-2', 
                formErrors.phone ? 'border-red-500' : 'border-black'
              ]"
              type="text"
              placeholder="請輸入連絡電話"
              v-model="inputClient.phone"
              @blur="validatePhoneInput"
              @input="validatePhoneInput"
            />
            <p v-if="formErrors.phone" class="text-red-500 text-sm mt-1">{{ formErrors.phone }}</p>
            <p v-else class="text-gray-500 text-xs mt-1">支援格式：手機 (0912345678)、市話 (02-12345678)</p>
          </div>
          <div class="col-span-2">
            <!-- <label for="birthday">Birthday:</label> -->
            <p>生日</p>

            <input
              class="w-full border-2 border-black"
              type="date"
              placeholder="請選擇生日"
              v-model="inputClient.birthday"
            />
          </div>
          <div class="col-span-2">
            <button type="submit" class="bg-black text-white w-full mt-4">確認修改</button>
          </div>
        </form>
      </div>

      <!-- 留言板內容 -->
      <div v-show="status == 'comments'">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-xl font-bold">我的留言</h3>
          <!-- 隱藏新增留言按鈕 -->
          <!-- <a-button type="default" class="border border-blue-500 text-blue-600 hover:bg-blue-50" @click="openCommentModal">
            <i class="fas fa-plus mr-1"></i> 新增留言
          </a-button> -->
        </div>

        <!-- 載入中 -->
        <div v-if="loadingComments" class="text-center py-8">
          <a-spin />
          <p class="mt-2 text-gray-500">載入留言中...</p>
        </div>

        <!-- 無留言時 -->
        <div v-else-if="userComments.length === 0" class="text-center py-8 border rounded-lg">
          <p class="text-gray-500 mb-4">您還沒有任何留言</p>
          <button 
            class="bg-blue-500 text-white hover:bg-blue-600 font-medium py-2 px-6 rounded transition-colors"
            @click="openCommentModal"
          >
            <i class="fas fa-plus mr-1"></i> 新增留言
          </button>
        </div>

        <!-- 聊天視窗 -->
        <div v-else class="border rounded-lg" style="height: 720px; position: relative;">
          <!-- 聊天內容區域 -->
          <div class="p-4 overflow-y-auto" style="height: calc(100% - 60px); padding-bottom: 70px;">
            <!-- 所有訊息按時間排序 -->
            <div v-for="item in sortedMessages" :key="item.id" class="mb-4">
              <!-- 用戶留言（右側） -->
              <div v-if="item.type === 'user'" class="flex items-start justify-end">
                <div class="mr-2 text-right">
                  <!-- 添加留言類型標籤 -->
                  <div class="text-xs text-gray-400 mb-1">
                    <template v-if="item.productId">
                      商品留言：{{ item.product || '未知商品' }}
                    </template>
                    <template v-else>
                      一般留言
                    </template>
                  </div>
                  <div class="bg-blue-100 rounded-lg px-4 py-2 text-blue-800 max-w-xs">
                    {{ item.content }}
                  </div>
                  <div class="text-xs text-gray-500 mt-1">{{ formatDateTime(item.time) }}</div>
                </div>
                <div class="flex-shrink-0">
                  <i class="fas fa-user-circle text-3xl text-blue-400"></i>
                </div>
              </div>
              
              <!-- 管理員回覆（左側） -->
              <div v-else class="flex items-start">
                <div class="flex-shrink-0">
                  <i class="fas fa-user-shield text-3xl text-gray-400"></i>
                </div>
                <div class="ml-2">
                  <!-- 添加留言類型標籤 -->
                  <div class="text-xs text-gray-400 mb-1">
                    <template v-if="item.productId">
                      商品留言：{{ item.product || '未知商品' }}
                    </template>
                    <template v-else>
                      一般留言
                    </template>
                  </div>
                  <div class="bg-gray-100 rounded-lg px-4 py-2 text-gray-800 max-w-xs">
                    {{ item.content }}
                  </div>
                  <div class="text-xs text-gray-400 mt-1">管理員回覆</div>
                </div>
              </div>
            </div>
            
            <!-- 添加額外的底部間距，確保內容不被輸入框遮擋 -->
            <div class="h-16"></div>
          </div>
          
          <!-- 固定在底部的輸入區域 -->
          <div class="absolute bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-3 rounded-b-lg">
            <div class="flex">
              <input 
                v-model="newMessage"
                type="text"
                class="border rounded-l px-4 py-2 w-full"
                placeholder="輸入留言內容..."
                @keyup.enter="sendNewMessage"
              />
              <button 
                class="bg-blue-500 text-white px-4 py-2 rounded-r hover:bg-blue-600"
                @click="sendNewMessage"
              >
                發送
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- <CartItem :cartData="productData"></CartItem> -->
    </div>

    <!-- 訂單分頁 - 固定在底部 -->
    <div v-show="status == 'order'" class="mt-4">
      <!-- 桌面版分頁 -->
      <div v-if="orders && orders.length > 0" class="hidden md:flex justify-center">
        <a-pagination
          v-model:current="pagination.current"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :show-size-changer="pagination.showSizeChanger"
          :show-quick-jumper="pagination.showQuickJumper"
          :page-size-options="pagination.pageSizeOptions"
          :show-total="pagination.showTotal"
          @change="handlePageChange"
          @showSizeChange="handleShowSizeChange"
          size="default"
        />
      </div>
      
      <!-- 手機版分頁 -->
      <div v-if="orders && orders.length > 0" class="block md:hidden flex justify-center">
        <a-pagination
          v-model:current="pagination.current"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :show-size-changer="false"
          :show-quick-jumper="false"
          :show-total="pagination.showTotal"
          @change="handlePageChange"
          @showSizeChange="handleShowSizeChange"
          size="small"
          simple
        />
      </div>
    </div>

    <!-- 留言彈窗 -->
    <a-modal
      v-model:open="commentModalVisible"
      title="新增留言"
      :maskClosable="false"
      :footer="null"
      :width="500"
      :bodyStyle="{ padding: '20px' }"
    >
      <p class="mb-2">請輸入您的留言：</p>
      <a-textarea
        v-model:value="commentContent"
        placeholder="請輸入留言內容..."
        :rows="6"
        :maxLength="500"
        showCount
      />
      <div class="flex justify-end mt-4 space-x-2">
        <button 
          class="bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-2 px-4 rounded"
          @click="commentModalVisible = false"
        >
          取消
        </button>
        <button 
          class="bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded"
          @click="submitComment"
          :disabled="commentSubmitting"
        >
          <span v-if="commentSubmitting">處理中...</span>
          <span v-else>提交留言</span>
        </button>
      </div>
    </a-modal>
  </div>
</template>

<style></style>
