import{_ as ze,q as De,s as Be,H as we,r as v,a as He,N as Ve,G as Je,o as Qe,O as L,S as Ye,P as Fe,c as se,d as P,e as i,f as c,g as s,t as y,n as We,h as d,w as g,u as Q,F as R,i as Y,j as C,p,K as _e,C as Xe,I as Ge,v as x,T as Ke,l as oe,z as ae,m as Ze}from"./index-c4c5a295.js";import{P as et,N as tt,A as lt,S as xe,a as st}from"./autoplay-7387adc2.js";const ot={class:"product-info-container container m-auto px-4 pt-8"},at={key:0,class:"loading-container"},nt={class:"flex justify-center items-center mb-8"},rt={class:"text-lg"},it={key:1,class:"error-container text-center py-12"},ct={class:"text-gray-600 mb-6"},dt={class:"space-x-4"},ut={key:2},vt={class:"flex flex-col lg:flex-row items-start"},gt={class:"lg:w-5/12 lg:sticky lg:top-24"},mt={class:"group relative cursor-[zoom-in] overflow-hidden h-full"},pt=["src","alt"],bt={key:0,class:"thumbnail-container"},ht={class:"flex gap-2 overflow-x-auto pb-2"},ft=["onClick"],yt=["src","alt"],wt={class:"lg:w-7/12 text-center lg:text-left lg:pl-16 flex flex-col"},_t={class:"font-bold text-4xl mb-4"},xt={key:0,class:"des border-bottom mb-4"},kt=["innerHTML"],Ct={class:"my-4"},Tt={key:0,class:"flex items-baseline space-x-2"},St={class:"text-4xl font-extrabold text-red-600"},Et={key:1,class:"text-4xl font-extrabold text-gray-900"},$t={key:1,class:"my-6"},Lt={class:"font-semibold mb-2"},Pt={class:"flex flex-wrap gap-2"},It=["onClick"],Mt={class:"mt-auto"},Nt={key:0},Ut={class:"bg-gray-200 inline-block px-2 border-2 border-black"},qt={class:"flex items-center justify-center lg:justify-start"},At={class:"mt-6 text-center lg:text-left"},Ot={class:"my-8"},Rt={class:"mb-3"},jt=["innerHTML"],zt=["innerHTML"],Dt={class:"mb-2"},Bt={class:"space-y-6"},Ht={class:"flex items-center space-x-4 p-4 bg-gray-50 rounded-lg"},Vt=["src","alt"],Jt={class:"flex-1"},Qt={class:"font-semibold"},Yt={class:"text-gray-600"},Ft={class:"flex"},Wt={class:"flex space-x-3"},Xt={__name:"ProductInfo",setup(Gt){const F=De(),{isUser:k}=Be(F),ne=we(["商品說明","商品規格"]),j=v(ne[0]),{postApiData:ke,getApiData:Kt,delApiData:Zt,cancelRequests:el,getProductById:re}=He(),z=Ve();z.query;const Ce=[et,tt,lt],o=v(null),ie=Je(),I=v(!0),E=v(null),W=v("載入商品資訊..."),f=v([]),M=v([]),w=we({}),X=v(0),D=v(null),ce=v(!1);Qe(async()=>{await L(),document.documentElement.scrollTop=0,document.body.scrollTop=0,window.scrollTo({top:0,left:0,behavior:"instant"}),setTimeout(()=>{window.scrollTo({top:0,left:0,behavior:"instant"})},0),window.addEventListener("scroll",h),document.addEventListener("scroll",h),document.documentElement.addEventListener("scroll",h),document.body.addEventListener("scroll",h),setTimeout(()=>{window.addEventListener("scroll",h),document.addEventListener("scroll",h),document.documentElement.addEventListener("scroll",h),document.body.addEventListener("scroll",h)},1e3);try{if(I.value=!0,E.value=null,!z.query.id)throw new Error("商品ID未提供");W.value="正在載入商品資訊...";const t=await re(z.query.id);if(!t||!t.success||!t.product)throw new Error("找不到此商品");const e=t.product;if(o.value=e,W.value="載入商品規格...",e.specTypes)try{f.value=JSON.parse(e.specTypes),f.value.forEach(l=>{w[l.name]=null})}catch(l){console.error("規格類型解析失敗:",l),f.value=[]}if(e.specCombinations)try{M.value=JSON.parse(e.specCombinations)}catch(l){console.error("規格組合解析失敗:",l),M.value=[]}await L(),window.scrollTo({top:0,left:0,behavior:"instant"}),await L(),B(),setTimeout(()=>{Te(),B(),de()},100),ee(),window.addEventListener("resize",ee)}catch(t){console.error("💥 載入商品失敗:",t),E.value=t.message||"載入商品失敗，請稍後再試"}finally{I.value=!1}}),Ye(()=>{window.removeEventListener("scroll",h),document.removeEventListener("scroll",h),document.documentElement.removeEventListener("scroll",h),document.body.removeEventListener("scroll",h),window.removeEventListener("resize",ee)});const Te=()=>{const t=document.querySelector(".highlights-content");if(!t)return;t.querySelectorAll("*[style]").forEach(l=>{const a=l.getAttribute("style");if(!a)return;a.split(";").filter(b=>b.trim()).forEach(b=>{const[T,V]=b.split(":").map(J=>J.trim());T&&V&&l.style.setProperty(T,V,"important")})})},B=()=>{const t=document.querySelector(".highlights-content");if(!t)return;t.querySelectorAll('*[style*="color"]').forEach(a=>{const n=a.getAttribute("style");n&&(n.includes("hsl(120, 75%, 60%)")?(a.style.setProperty("color","hsl(120, 75%, 60%)","important"),a.classList.add("highlight-green")):n.includes("hsl(240, 75%, 60%)")?(a.style.setProperty("color","hsl(240, 75%, 60%)","important"),a.classList.add("highlight-blue")):(n.includes("rgb(255, 0, 0)")||n.includes("rgb(255,0,0)"))&&(a.style.setProperty("color","rgb(255, 0, 0)","important"),a.classList.add("highlight-red")))}),t.querySelectorAll("[data-color]").forEach(a=>{switch(a.getAttribute("data-color")){case"red":a.style.setProperty("color","rgb(255, 0, 0)","important");break;case"blue":a.style.setProperty("color","hsl(240, 75%, 60%)","important");break;case"green":a.style.setProperty("color","hsl(120, 75%, 60%)","important");break;case"gray":a.style.setProperty("color","rgb(77, 81, 86)","important");break}})},de=()=>{if(!document.querySelector(".highlights-content"))return;const e="force-highlights-colors";let l=document.getElementById(e);l&&l.remove();const a=document.createElement("style");a.id=e,a.textContent=`
    .highlights-content span[style*="color:hsl(120, 75%, 60%)"],
    .highlights-content span[style*="color: hsl(120, 75%, 60%)"] {
      color: hsl(120, 75%, 60%) !important;
    }

    .highlights-content span[style*="color:hsl(240, 75%, 60%)"],
    .highlights-content span[style*="color: hsl(240, 75%, 60%)"] {
      color: hsl(240, 75%, 60%) !important;
    }

    .highlights-content span[style*="color:rgb(255,0,0)"],
    .highlights-content span[style*="color: rgb(255, 0, 0)"] {
      color: rgb(255, 0, 0) !important;
    }

    .highlights-content .highlight-green {
      color: hsl(120, 75%, 60%) !important;
    }

    .highlights-content .highlight-blue {
      color: hsl(240, 75%, 60%) !important;
    }

    .highlights-content .highlight-red {
      color: rgb(255, 0, 0) !important;
    }
  `,document.head.appendChild(a)};Fe(()=>z.query.id,async(t,e)=>{if(t&&t!==e){await L(),document.documentElement.scrollTop=0,document.body.scrollTop=0,window.scrollTo({top:0,left:0,behavior:"instant"}),setTimeout(()=>{window.scrollTo({top:0,left:0,behavior:"instant"})},0);try{I.value=!0,E.value=null;const l=await re(t);if(!l||!l.success||!l.product)throw new Error("找不到此商品");const a=l.product;if(o.value=a,a.specTypes)try{f.value=JSON.parse(a.specTypes),Object.keys(w).forEach(n=>{delete w[n]}),f.value.forEach(n=>{w[n.name]=null})}catch(n){console.error("規格類型解析失敗:",n),f.value=[]}if(a.specCombinations)try{M.value=JSON.parse(a.specCombinations)}catch(n){console.error("規格組合解析失敗:",n),M.value=[]}await L(),window.scrollTo({top:0,left:0,behavior:"instant"}),await L(),B(),setTimeout(()=>{B(),de()},100)}catch(l){console.error("💥 重新載入商品失敗:",l),E.value=l.message||"載入商品失敗，請稍後再試"}finally{I.value=!1}}});const Se=(t,e)=>{w[t]===e?w[t]=null:w[t]=e},m=se(()=>{if(!Object.values(w).every(l=>l!==null))return null;const e=f.value.map(l=>w[l.name]);return M.value.find(l=>JSON.stringify(l.specs)===JSON.stringify(e))}),N=se(()=>{var t,e,l;return m.value?k.value&&m.value.specialPrice||m.value.price:k.value?((t=o.value)==null?void 0:t.price2)||((e=o.value)==null?void 0:e.price1):(l=o.value)==null?void 0:l.price1}),Ee=se(()=>{var e;if(!((e=o.value)!=null&&e.highlights))return"";let t=o.value.highlights;return t=t.replace(/(<[^>]*style="[^"]*color:\s*hsl\(120,\s*75%,\s*60%\)[^"]*"[^>]*>)/gi,"$1".replace(">",' data-force-color="green">')),t=t.replace(/(<[^>]*style="[^"]*color:\s*hsl\(240,\s*75%,\s*60%\)[^"]*"[^>]*>)/gi,"$1".replace(">",' data-force-color="blue">')),t=t.replace(/(<[^>]*style="[^"]*color:\s*rgb\(255,\s*0,\s*0\)[^"]*"[^>]*>)/gi,"$1".replace(">",' data-force-color="red">')),t=t.replace(/style="([^"]*color:\s*hsl\(120,\s*75%,\s*60%\)[^"]*)"/gi,'style="$1; color: hsl(120, 75%, 60%) !important;" class="highlight-green"'),t=t.replace(/style="([^"]*color:\s*hsl\(240,\s*75%,\s*60%\)[^"]*)"/gi,'style="$1; color: hsl(240, 75%, 60%) !important;" class="highlight-blue"'),t=t.replace(/style="([^"]*color:\s*rgb\(255,\s*0,\s*0\)[^"]*)"/gi,'style="$1; color: rgb(255, 0, 0) !important;" class="highlight-red"'),t}),$e=t=>{const e=t.target,l=e.getBoundingClientRect(),a=t.clientX-l.left,n=t.clientY-l.top,b=Math.max(0,Math.min(100,a/l.width*100)),T=Math.max(0,Math.min(100,n/l.height*100));e.style.transformOrigin=`${b}% ${T}%`},G=t=>Ze(t,"product"),Le=t=>{if(X.value=t,D.value&&D.value.$el){const e=D.value.$el.swiper;e&&e.slideTo(t)}},Pe=t=>{X.value=t.realIndex||t.activeIndex},Ie=()=>{if(f.value.length>0&&!m.value){x.warning("請選擇完整的商品規格");return}const t=localStorage.getItem("isLogin"),e=localStorage.getItem("token");t&&e&&!k.value&&(k.value=!0,console.log("已同步用戶登入狀態")),F.addCarts({id:o.value.id,name:o.value.name,count:1,price:N.value,img:o.value.images[0],specs:m.value?m.value.specs.join(", "):"",sku:m.value?m.value.sku:null,minPurchaseQty:o.value.minPurchaseQty||1})},Me=()=>{if(f.value.length>0&&!m.value){x.warning("請選擇完整的商品規格");return}const t=localStorage.getItem("isLogin"),e=localStorage.getItem("token");t&&e&&!k.value&&(k.value=!0,console.log("已同步用戶登入狀態")),F.addCarts({id:o.value.id,name:o.value.name,count:1,price:N.value,img:o.value.images[0],specs:m.value?m.value.specs.join(", "):"",sku:m.value?m.value.sku:null,minPurchaseQty:o.value.minPurchaseQty||1}),ie.push("/check")},H=v(!1),$=v(""),K=v(!1),Z=v(!1),U=v(""),q=v(""),A=v(!1),ee=()=>{const e=window.innerWidth<=768,l=navigator.userAgent||navigator.vendor||window.opera,n=/android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini|mobile/i.test(l.toLowerCase());A.value=e||n},Ne=()=>{var a,n,b;const t=window.location.href;U.value=t;const e=m.value?m.value.price:(a=o.value)==null?void 0:a.price1,l=(n=o.value)!=null&&n.description?o.value.description.replace(/<[^>]*>/g,"").substring(0,100)+"...":"精選商品";q.value=`🛍️ ${(b=o.value)==null?void 0:b.name}

📝 ${l}

💰 零售價：NT$ ${e}

🔗 立即查看：${t}

#優質商品 #限時推薦`},Ue=()=>{if(A.value)Ne(),Z.value=!0;else{const t=window.location.href;te(t)}},te=async t=>{try{await navigator.clipboard.writeText(t),x.success("已複製到剪貼板！")}catch{const l=document.createElement("textarea");l.value=t,document.body.appendChild(l),l.select(),document.execCommand("copy"),document.body.removeChild(l),x.success("已複製到剪貼板！")}},qe=()=>{te(q.value)},Ae=()=>{te(U.value)},le=t=>{var n;const e=encodeURIComponent(U.value),l=encodeURIComponent(`${(n=o.value)==null?void 0:n.name} - 精選商品推薦`);let a="";switch(t){case"facebook":a=`https://www.facebook.com/sharer/sharer.php?u=${e}`;break;case"line":a=`https://line.me/R/msg/text/?${encodeURIComponent(q.value)}`;break;case"twitter":a=`https://twitter.com/intent/tweet?url=${e}&text=${l}`;break;default:return}window.open(a,"_blank","width=600,height=400"),x.success(`已開啟${t}分享頁面`)},Oe=()=>{if(!k.value){x.warning("請先登入再進行留言"),setTimeout(()=>{ie.push("/login")},1e3);return}H.value=!0,$.value=""},Re=async()=>{if(!$.value.trim()){x.warning("留言內容不能為空");return}K.value=!0;try{const t=await ke("comments",{content:$.value,product:o.value.id});t&&t.status===201?(Ke.success({title:"留言成功",content:"您的留言已成功送出，感謝您的分享！",centered:!0}),H.value=!1,$.value=""):x.error((t==null?void 0:t.error)||"留言失敗，請稍後再試")}catch(t){console.error("提交留言時發生錯誤:",t),x.error("留言失敗，請稍後再試")}finally{K.value=!1}},h=()=>{const t=window.pageYOffset,e=document.documentElement.scrollTop,l=document.body.scrollTop,a=window.scrollY,b=(l||t||e||a||0)>100;ce.value=b},je=()=>{window.scrollTo(0,0),document.documentElement.scrollTop=0,document.body.scrollTop=0};return(t,e)=>{var J,ue,ve,ge,me,pe,be,he,fe,ye;const l=P("a-button"),a=P("a-tag"),n=P("a-segmented"),b=P("a-textarea"),T=P("a-modal"),V=P("a-input");return i(),c(R,null,[s("div",ot,[I.value?(i(),c("div",at,[s("div",nt,[e[11]||(e[11]=s("div",{class:"spinner border-4 border-blue-200 border-t-blue-600 rounded-full w-8 h-8 animate-spin mr-3"},null,-1)),s("span",rt,y(W.value),1)]),e[12]||(e[12]=We('<div class="flex flex-col lg:flex-row items-start animate-pulse" data-v-ec7be68b><div class="lg:w-5/12 lg:sticky lg:top-24" data-v-ec7be68b><div class="h-[500px] aspect-square bg-gray-200 rounded-lg mb-4" data-v-ec7be68b></div><div class="flex space-x-2" data-v-ec7be68b><div class="w-16 h-16 bg-gray-200 rounded" data-v-ec7be68b></div><div class="w-16 h-16 bg-gray-200 rounded" data-v-ec7be68b></div><div class="w-16 h-16 bg-gray-200 rounded" data-v-ec7be68b></div></div></div><div class="lg:w-7/12 text-center lg:text-left lg:pl-16 flex flex-col mt-8 lg:mt-0" data-v-ec7be68b><div class="h-10 bg-gray-200 rounded mb-4" data-v-ec7be68b></div><div class="h-12 bg-gray-200 rounded w-1/2 mb-6" data-v-ec7be68b></div><div class="mb-6" data-v-ec7be68b><div class="h-6 bg-gray-200 rounded w-1/4 mb-2" data-v-ec7be68b></div><div class="flex space-x-2" data-v-ec7be68b><div class="h-10 bg-gray-200 rounded w-20" data-v-ec7be68b></div><div class="h-10 bg-gray-200 rounded w-20" data-v-ec7be68b></div><div class="h-10 bg-gray-200 rounded w-20" data-v-ec7be68b></div></div></div><div class="flex space-x-4" data-v-ec7be68b><div class="h-10 bg-gray-200 rounded w-32" data-v-ec7be68b></div><div class="h-10 bg-gray-200 rounded w-32" data-v-ec7be68b></div></div><div class="flex space-x-4 mt-6" data-v-ec7be68b><div class="h-8 bg-gray-200 rounded w-24" data-v-ec7be68b></div><div class="h-8 bg-gray-200 rounded w-24" data-v-ec7be68b></div></div></div></div><div class="my-8" data-v-ec7be68b><div class="h-8 bg-gray-200 rounded w-1/3 mb-4" data-v-ec7be68b></div><div class="space-y-2" data-v-ec7be68b><div class="h-4 bg-gray-200 rounded" data-v-ec7be68b></div><div class="h-4 bg-gray-200 rounded w-5/6" data-v-ec7be68b></div><div class="h-4 bg-gray-200 rounded w-4/6" data-v-ec7be68b></div></div></div>',2))])):E.value?(i(),c("div",it,[e[15]||(e[15]=s("div",{class:"text-red-500 text-6xl mb-6"},[s("i",{class:"fas fa-exclamation-triangle"})],-1)),e[16]||(e[16]=s("h2",{class:"text-2xl font-bold text-gray-800 mb-4"},"載入失敗",-1)),s("p",ct,y(E.value),1),s("div",dt,[d(l,{type:"primary",size:"large",onClick:e[0]||(e[0]=r=>t.$router.go(-1))},{default:g(()=>e[13]||(e[13]=[s("i",{class:"fas fa-arrow-left mr-2"},null,-1),p("返回上頁 ")])),_:1,__:[13]}),d(l,{size:"large",onClick:e[1]||(e[1]=r=>t.window.location.reload())},{default:g(()=>e[14]||(e[14]=[s("i",{class:"fas fa-redo mr-2"},null,-1),p("重新載入 ")])),_:1,__:[14]})])])):o.value?(i(),c("div",ut,[s("div",vt,[s("div",gt,[d(Q(st),{ref_key:"swiperRef",ref:D,slidesPerView:1,spaceBetween:30,loop:((J=o.value)==null?void 0:J.images.length)>1,pagination:{clickable:!0},navigation:!0,modules:Ce,onSlideChange:Pe,class:"h-[500px] aspect-square product-swiper mb-4"},{default:g(()=>{var r,u,S;return[(r=o.value)!=null&&r.images&&((u=o.value)==null?void 0:u.images.length)>0?(i(!0),c(R,{key:0},Y((S=o.value)==null?void 0:S.images,O=>(i(),oe(Q(xe),{class:"",key:O},{default:g(()=>{var _;return[s("div",mt,[s("img",{src:G(O),alt:(_=o.value)==null?void 0:_.name,class:"group-hover:scale-[1.5] duration-300 w-full h-full object-contain",onMousemove:$e},null,40,pt)])]}),_:2},1024))),128)):(i(),oe(Q(xe),{key:1},{default:g(()=>e[17]||(e[17]=[s("div",{class:"aspect-square flex items-center justify-center bg-transparent text-gray-500"}," 無圖片 ",-1)])),_:1,__:[17]}))]}),_:1},8,["loop"]),(ue=o.value)!=null&&ue.images&&((ve=o.value)==null?void 0:ve.images.length)>1?(i(),c("div",bt,[s("div",ht,[(i(!0),c(R,null,Y(o.value.images,(r,u)=>(i(),c("div",{key:`thumb-${u}`,onClick:S=>Le(u),class:ae(["thumbnail-item","flex-shrink-0","w-20 h-20","border-2 rounded-lg overflow-hidden cursor-pointer transition-all",X.value===u?"border-blue-500 shadow-lg":"border-gray-300 hover:border-gray-400"])},[s("img",{src:G(r),alt:`${o.value.name} - 圖片 ${u+1}`,class:"w-full h-full object-cover"},null,8,yt)],10,ft))),128))])])):C("",!0)]),s("div",wt,[s("div",null,[s("h3",_t,y((ge=o.value)==null?void 0:ge.name),1),(me=o.value)!=null&&me.highlights&&o.value.highlights.trim()!==""?(i(),c("div",xt,[s("div",{innerHTML:Ee.value,class:"rich-text-content highlights-content"},null,8,kt)])):C("",!0),s("div",Ct,[Q(k)?(i(),c("div",Tt,[d(a,{color:"red",class:"text-base"},{default:g(()=>e[18]||(e[18]=[p("會員價")])),_:1,__:[18]}),s("span",St,[e[19]||(e[19]=p(" NT$ ")),d(_e,{number:N.value},null,8,["number"])])])):(i(),c("div",Et,[e[20]||(e[20]=p(" NT$ ")),d(_e,{number:N.value},null,8,["number"])]))]),f.value.length>0?(i(),c("div",$t,[(i(!0),c(R,null,Y(f.value,r=>(i(),c("div",{key:r.name,class:"mb-4"},[s("h4",Lt,y(r.name),1),s("div",Pt,[(i(!0),c(R,null,Y(r.options,u=>(i(),c("button",{key:u.value,onClick:S=>Se(r.name,u.value),class:ae(["px-4 py-2 border rounded-md transition-colors",w[r.name]===u.value?"bg-blue-600 text-white border-blue-600":"bg-white text-gray-700 border-gray-300 hover:border-blue-500"])},y(u.value),11,It))),128))])]))),128))])):C("",!0)]),s("div",Mt,[(pe=o.value)!=null&&pe.format2?(i(),c("div",Nt,[s("div",null,y((be=o.value)==null?void 0:be.format1),1),s("div",Ut,y((he=o.value)==null?void 0:he.format2),1)])):C("",!0),s("div",qt,[d(l,{class:"mr-4",size:"large",onClick:Ie},{default:g(()=>e[21]||(e[21]=[p(" 加入購物車 ")])),_:1,__:[21]}),d(l,{type:"primary",size:"large",onClick:Me},{default:g(()=>e[22]||(e[22]=[p(" 立即購買 ")])),_:1,__:[22]})]),s("div",At,[d(l,{type:"link",onClick:Oe},{default:g(()=>e[23]||(e[23]=[s("i",{class:"fas fa-comment-dots mr-2"},null,-1),p(" 我要留言 ")])),_:1,__:[23]}),d(l,{type:"link",onClick:Ue,class:"ml-4"},{default:g(()=>[s("i",{class:ae(A.value?"fas fa-share-alt mr-2":"fas fa-copy mr-2")},null,2),p(" "+y(A.value?"分享商品":"複製連結"),1)]),_:1})])])])]),s("div",Ot,[s("div",Rt,[d(n,{value:j.value,"onUpdate:value":e[2]||(e[2]=r=>j.value=r),valueModifiers:{trim:!0,lazy:!0},options:ne},null,8,["value","options"])]),j.value=="商品說明"?(i(),c("div",{key:0,innerHTML:(fe=o.value)==null?void 0:fe.description,class:"rich-text-content"},null,8,jt)):j.value=="商品規格"?(i(),c("div",{key:1,innerHTML:(ye=o.value)==null?void 0:ye.desc2,class:"rich-text-content"},null,8,zt)):C("",!0)])])):C("",!0)]),d(T,{open:H.value,"onUpdate:open":e[4]||(e[4]=r=>H.value=r),title:"留言此商品",maskClosable:!1,onOk:Re,okText:"提交留言",cancelText:"取消",confirmLoading:K.value},{default:g(()=>{var r;return[s("p",Dt,"請輸入您對「"+y((r=o.value)==null?void 0:r.name)+"」的留言：",1),d(b,{value:$.value,"onUpdate:value":e[3]||(e[3]=u=>$.value=u),placeholder:"請輸入留言內容...",rows:4,maxLength:500,showCount:""},null,8,["value"])]}),_:1},8,["open","confirmLoading"]),d(T,{open:Z.value,"onUpdate:open":e[10]||(e[10]=r=>Z.value=r),title:"分享商品",footer:null,width:"600px",maskClosable:!0,centered:""},{default:g(()=>{var r,u,S,O;return[s("div",Bt,[s("div",Ht,[(u=(r=o.value)==null?void 0:r.images)!=null&&u[0]?(i(),c("img",{key:0,src:G(o.value.images[0]),alt:(S=o.value)==null?void 0:S.name,class:"w-16 h-16 object-cover rounded"},null,8,Vt)):C("",!0),s("div",Jt,[s("h4",Qt,y((O=o.value)==null?void 0:O.name),1),s("p",Yt,"NT$ "+y(N.value),1)])]),s("div",null,[e[25]||(e[25]=s("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"分享內容",-1)),d(b,{value:q.value,"onUpdate:value":e[5]||(e[5]=_=>q.value=_),rows:6,readonly:"",class:"bg-gray-50"},null,8,["value"]),d(l,{type:"link",onClick:qe,class:"mt-2 p-0"},{default:g(()=>e[24]||(e[24]=[s("i",{class:"fas fa-copy mr-1"},null,-1),p("複製內容 ")])),_:1,__:[24]})]),s("div",null,[e[27]||(e[27]=s("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"商品連結",-1)),s("div",Ft,[d(V,{value:U.value,"onUpdate:value":e[6]||(e[6]=_=>U.value=_),readonly:"",class:"flex-1"},null,8,["value"]),d(l,{type:"primary",onClick:Ae,class:"ml-2"},{default:g(()=>e[26]||(e[26]=[s("i",{class:"fas fa-copy mr-1"},null,-1),p("複製 ")])),_:1,__:[26]})])]),s("div",null,[e[31]||(e[31]=s("label",{class:"block text-sm font-medium text-gray-700 mb-3"},"分享到社群媒體",-1)),s("div",Wt,[d(l,{onClick:e[7]||(e[7]=_=>le("facebook")),class:"flex-1 text-blue-600 border-blue-600 hover:bg-blue-50"},{default:g(()=>e[28]||(e[28]=[s("i",{class:"fab fa-facebook-f mr-2"},null,-1),p("Facebook ")])),_:1,__:[28]}),A.value?(i(),oe(l,{key:0,onClick:e[8]||(e[8]=_=>le("line")),class:"flex-1 text-green-600 border-green-600 hover:bg-green-50"},{default:g(()=>e[29]||(e[29]=[s("i",{class:"fab fa-line mr-2"},null,-1),p("LINE ")])),_:1,__:[29]})):C("",!0),d(l,{onClick:e[9]||(e[9]=_=>le("twitter")),class:"flex-1 text-blue-400 border-blue-400 hover:bg-blue-50"},{default:g(()=>e[30]||(e[30]=[s("i",{class:"fab fa-twitter mr-2"},null,-1),p("Twitter ")])),_:1,__:[30]})])])])]}),_:1},8,["open"]),Xe(s("div",{onClick:je,class:"back-to-top-btn",title:"回到頂部"}," ↑ ",512),[[Ge,ce.value]])],64)}}},sl=ze(Xt,[["__scopeId","data-v-ec7be68b"]]);export{sl as default};
