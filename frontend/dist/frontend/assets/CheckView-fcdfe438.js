import{_ as j,G as F,q as M,s as P,r as L,H as U,c as X,P as z,o as E,b as O,x as A,y as G,v as H,d as J,e as n,f as u,u as V,g as t,h as w,t as _,B as K,p as c,C as g,E as f,z as $,j as y,w as S,F as Q,L as W}from"./index-c4c5a295.js";const T=""+new URL("task-df33db08.svg",import.meta.url).href;const Y={key:0,class:"container m-auto px-6 xl:px-0 py-10"},Z={class:"grid grid-cols-1 xl:grid-cols-3 gap-10"},ee={class:"bg-gray-200 p-8 rounded-lg md:col-span-2"},te={class:"flex justify-end mt-4 border-t-2 border-gray-400 pt-4"},se={class:"text-2xl font-bold"},oe={class:"xl:mt-20"},re={class:"mb-4"},le={key:0,class:"text-red-500 text-sm mt-1"},ae={class:"mb-4"},de={key:0,class:"text-red-500 text-sm mt-1"},ie={key:1,class:"text-gray-500 text-sm mt-1"},ne={class:"mb-4"},ue={key:0,class:"text-red-500 text-sm mt-1"},pe={class:"mb-4"},ve={key:0,class:"text-red-500 text-sm mt-1"},ce={class:"mb-4"},me={class:"mb-4"},ge={class:"mb-4 col-span-2"},fe={class:"col-span-2"},be={key:0,class:"text-red-500 text-center mb-2"},xe=["disabled"],_e={key:1,class:"container m-auto py-10 text-center flex flex-col items-center",style:{"min-height":"50vh"}},ye={class:"mt-10"},he={key:2,class:"container m-auto text-center py-20"},ke={class:"flex flex-col justify-center items-center"},we={class:"mt-10"},$e={__name:"CheckView",setup(Ie){const h=F(),q=M(),{clientData:I,carts:d,total:k,isUser:B}=P(q),m=L(!1),o=U({receiver:"",phone:"",detailed_address:"",cooperative:"",invoice_title:"",tax_id:"",notes:""}),r=U({receiver:"",phone:"",detailed_address:"",cooperative:"",invoice_title:"",tax_id:"",notes:""}),l=U({receiver:!1,phone:!1,detailed_address:!1,cooperative:!1,invoice_title:!1,tax_id:!1,notes:!1}),i=L(""),D=a=>{if(!/^[0-9]+$/.test(a))return"電話號碼只能包含數字";const p=/^09\d{8}$/,s=/^0\d{1,2}\d{6,8}$/;return!p.test(a)&&!s.test(a)?"請輸入有效的台灣電話號碼格式":""},v=()=>{o.receiver.trim()?r.receiver="":r.receiver="請輸入訂購人姓名",o.phone.trim()?r.phone=D(o.phone):r.phone="請輸入聯絡電話",o.detailed_address.trim()?r.detailed_address="":r.detailed_address="請輸入詳細住址",o.cooperative.trim()?r.cooperative="":r.cooperative="請輸入儲互社資訊"},b=a=>{l[a]=!0,v()},N=a=>{const e=a.target.value;o.phone=e.replace(/\D/g,""),l.phone=!0,v()},C=X(()=>o.receiver.trim()!==""&&o.phone.trim()!==""&&o.detailed_address.trim()!==""&&o.cooperative.trim()!==""&&r.receiver===""&&r.phone===""&&r.detailed_address===""&&r.cooperative==="");z(d,a=>{(!a||a.length===0)&&!m.value&&(console.log("購物車已清空，導向首頁"),setTimeout(()=>{!m.value&&(!d.value||d.value.length===0)&&h.push("/")},100))},{deep:!0}),E(()=>{const a=localStorage.getItem("token"),e=localStorage.getItem("isLogin");if(!e||!a){localStorage.setItem("redirect_after_login",window.location.href),h.push("/login");return}if(e&&a&&!B.value&&(B.value=!0,console.log("已同步設置用戶登入狀態")),(!d.value||d.value.length===0)&&!m.value){console.log("購物車為空，導向首頁"),h.push("/");return}I.value&&(o.receiver=I.value.name||"",o.phone=I.value.phone||"",o.receiver&&(l.receiver=!0),o.phone&&(l.phone=!0),v())});const R=O.throttle(async()=>{var a,e,p;if(i.value="",l.receiver=!0,l.phone=!0,l.detailed_address=!0,l.cooperative=!0,v(),!C.value){i.value="請填寫所有必填欄位並修正錯誤";return}if(!d.value||d.value.length===0){i.value="購物車是空的，請先添加商品",h.push("/");return}if(!k.value||k.value<=0){i.value="訂單總金額有誤，請重新整理頁面";return}try{const s={receiver:o.receiver,phone:o.phone,address:"自取/無須配送",detailed_address:o.detailed_address,cooperative:o.cooperative,invoice_title:o.invoice_title,tax_id:o.tax_id,notes:o.notes,products:[...d.value],total:k.value,client:((a=I.value)==null?void 0:a.id)||"1",status:1};console.log("送出訂單資料:",s);const x=await A.post(G("orderList"),s,{headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("token")}`}});console.log("訂單提交結果:",x.data),x.data&&(x.data.success||x.status==200||x.status==201)?(m.value=!0,d.value=[],k.value=0,localStorage.setItem("carts",JSON.stringify([])),H.success("訂單提交成功！"),setTimeout(()=>{m.value&&h.push("/account")},3e3)):i.value=((e=x.data)==null?void 0:e.message)||"訂單提交失敗，請稍後再試"}catch(s){console.error("訂單提交錯誤:",s),s.response?i.value=`訂單提交失敗：${s.response.status} - ${((p=s.response.data)==null?void 0:p.message)||"伺服器錯誤"}`:s.request?i.value="無法連接到伺服器，請檢查網路連接並稍後再試":i.value=`訂單提交過程中發生錯誤：${s.message}`}},2e3,{trailing:!1});return(a,e)=>{const p=J("RouterLink");return n(),u(Q,null,[V(d).length>0?(n(),u("div",Y,[t("div",Z,[t("div",ee,[e[18]||(e[18]=t("h3",{class:"text-3xl font-bold mb-6"},"購物明細",-1)),w(W),t("div",te,[t("p",se,"訂單總金額: NT$ "+_(V(k).toLocaleString()),1)])]),t("div",oe,[e[26]||(e[26]=t("h4",{class:"text-3xl font-bold"},"訂購資料",-1)),e[27]||(e[27]=t("p",{class:"text-gray-600 mb-4"},null,-1)),t("form",{onSubmit:e[17]||(e[17]=K((...s)=>V(R)&&V(R)(...s),["prevent"])),class:"grid grid-cols-2 gap-4 mt-4"},[t("div",re,[e[19]||(e[19]=t("label",{for:"receiver",class:"block mb-1"},[c(" 訂購人 "),t("span",{class:"text-red-500"},"*")],-1)),g(t("input",{id:"receiver",class:$(["w-full border-2 p-2 rounded",{"border-red-500":l.receiver&&r.receiver}]),placeholder:"請輸入姓名","onUpdate:modelValue":e[0]||(e[0]=s=>o.receiver=s),onInput:e[1]||(e[1]=s=>b("receiver")),onBlur:e[2]||(e[2]=s=>{l.receiver=!0,v()}),required:""},null,34),[[f,o.receiver]]),l.receiver&&r.receiver?(n(),u("p",le,_(r.receiver),1)):y("",!0)]),t("div",ae,[e[20]||(e[20]=t("label",{for:"phone",class:"block mb-1"},[c(" 聯絡電話 "),t("span",{class:"text-red-500"},"*")],-1)),g(t("input",{id:"phone",class:$(["w-full border-2 p-2 rounded",{"border-red-500":l.phone&&r.phone}]),placeholder:"請輸入連絡電話 (僅數字)","onUpdate:modelValue":e[3]||(e[3]=s=>o.phone=s),onInput:N,onBlur:e[4]||(e[4]=s=>{l.phone=!0,v()}),required:""},null,34),[[f,o.phone]]),l.phone&&r.phone?(n(),u("p",de,_(r.phone),1)):(n(),u("p",ie," 請輸入有效的台灣手機或市話號碼 "))]),t("div",ne,[e[21]||(e[21]=t("label",{for:"detailed_address",class:"block mb-1"},[c(" 住址（詳細地址） "),t("span",{class:"text-red-500"},"*")],-1)),g(t("input",{id:"detailed_address",class:$(["w-full border-2 p-2 rounded",{"border-red-500":l.detailed_address&&r.detailed_address}]),placeholder:"請輸入詳細住址","onUpdate:modelValue":e[5]||(e[5]=s=>o.detailed_address=s),onInput:e[6]||(e[6]=s=>b("detailed_address")),onBlur:e[7]||(e[7]=s=>{l.detailed_address=!0,v()}),required:""},null,34),[[f,o.detailed_address]]),l.detailed_address&&r.detailed_address?(n(),u("p",ue,_(r.detailed_address),1)):y("",!0)]),t("div",pe,[e[22]||(e[22]=t("label",{for:"cooperative",class:"block mb-1"},[c(" 儲互社（XX區XX社） "),t("span",{class:"text-red-500"},"*")],-1)),g(t("input",{id:"cooperative",class:$(["w-full border-2 p-2 rounded",{"border-red-500":l.cooperative&&r.cooperative}]),placeholder:"請輸入儲互社資訊","onUpdate:modelValue":e[8]||(e[8]=s=>o.cooperative=s),onInput:e[9]||(e[9]=s=>b("cooperative")),onBlur:e[10]||(e[10]=s=>{l.cooperative=!0,v()}),required:""},null,34),[[f,o.cooperative]]),l.cooperative&&r.cooperative?(n(),u("p",ve,_(r.cooperative),1)):y("",!0)]),t("div",ce,[e[23]||(e[23]=t("label",{for:"invoice_title",class:"block mb-1"}," 公司抬頭 ",-1)),g(t("input",{id:"invoice_title",class:"w-full border-2 p-2 rounded",placeholder:"可選填","onUpdate:modelValue":e[11]||(e[11]=s=>o.invoice_title=s),onInput:e[12]||(e[12]=s=>b("invoice_title"))},null,544),[[f,o.invoice_title]])]),t("div",me,[e[24]||(e[24]=t("label",{for:"tax_id",class:"block mb-1"}," 統編 ",-1)),g(t("input",{id:"tax_id",class:"w-full border-2 p-2 rounded",placeholder:"可選填","onUpdate:modelValue":e[13]||(e[13]=s=>o.tax_id=s),onInput:e[14]||(e[14]=s=>b("tax_id"))},null,544),[[f,o.tax_id]])]),t("div",ge,[e[25]||(e[25]=t("label",{for:"notes",class:"block mb-1"}," 備註 ",-1)),g(t("textarea",{id:"notes",class:"w-full border-2 p-2 rounded",placeholder:"可選填",rows:"3","onUpdate:modelValue":e[15]||(e[15]=s=>o.notes=s),onInput:e[16]||(e[16]=s=>b("notes"))},null,544),[[f,o.notes]])]),t("div",fe,[i.value?(n(),u("p",be,_(i.value),1)):y("",!0),t("button",{type:"submit",class:$(["bg-black text-white w-full p-3 rounded-lg hover:bg-gray-800 transition mt-4",{"opacity-50 cursor-not-allowed":!C.value}]),disabled:!C.value}," 送出訂單 ",10,xe)])],32)])])])):y("",!0),m.value?(n(),u("div",_e,[e[30]||(e[30]=t("img",{src:T,class:"w-20",alt:"menu"},null,-1)),e[31]||(e[31]=t("p",{class:"text-2xl mt-4 mb-6"},"成功送出訂單",-1)),e[32]||(e[32]=t("p",{class:"text-gray-600 mb-4"},"您的訂單已成功提交，請到店自取商品",-1)),t("div",ye,[w(p,{to:"/",class:"mr-4 bg-gray-800 text-white px-6 py-2 rounded-lg hover:bg-black"},{default:S(()=>e[28]||(e[28]=[c(" 返回首頁 ")])),_:1,__:[28]}),w(p,{to:"/account",class:"bg-gray-600 text-white px-6 py-2 rounded-lg hover:bg-gray-700"},{default:S(()=>e[29]||(e[29]=[c(" 查看訂單 ")])),_:1,__:[29]})])])):m.value?(n(),u("div",he,[t("div",ke,[e[35]||(e[35]=t("img",{src:T,class:"w-20",alt:"menu"},null,-1)),e[36]||(e[36]=t("p",{class:"text-2xl mt-4 mb-6"},"成功送出訂單",-1)),e[37]||(e[37]=t("p",{class:"text-gray-600 mb-4"},"您的訂單已成功提交，請到店自取商品",-1)),t("div",we,[w(p,{to:"/",class:"mr-4 bg-gray-800 text-white px-6 py-2 rounded-lg hover:bg-black"},{default:S(()=>e[33]||(e[33]=[c(" 返回首頁 ")])),_:1,__:[33]}),w(p,{to:"/account",class:"bg-gray-600 text-white px-6 py-2 rounded-lg hover:bg-gray-700"},{default:S(()=>e[34]||(e[34]=[c(" 查看訂單 ")])),_:1,__:[34]})])])])):y("",!0)],64)}}},Se=j($e,[["__scopeId","data-v-6bc166e7"]]);export{Se as default};
